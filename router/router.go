package router

import (
	"solve-api/config"
	"solve-api/controllers"
	"solve-api/middleware"
	"solve-api/services"

	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	"gorm.io/gorm"
)

func SetupRouter(db *gorm.DB, rdb *redis.Client, cfg *config.Config) *gin.Engine {
	// 设置Gin模式
	gin.SetMode(cfg.Server.Mode)

	r := gin.New()

	// 中间件
	r.Use(gin.Logger())
	r.Use(gin.Recovery())
	r.Use(middleware.CORSMiddleware())

	// 初始化服务
	userService := services.NewUserService(db)
	agentService := services.NewAgentService(db)
	pointCardService := services.NewPointCardService(db)
	solveService := services.NewSolveService(db, cfg)
	uploadService, _ := services.NewUploadService(cfg)

	// 初始化控制器
	authController := controllers.NewAuthController(userService, cfg)
	userController := controllers.NewUserController(userService)
	agentController := controllers.NewAgentController(agentService)
	pointCardController := controllers.NewPointCardController(pointCardService)
	solveController := controllers.NewSolveController(solveService, uploadService)

	// 公开路由
	public := r.Group("/api/v1")
	{
		// 认证相关
		auth := public.Group("/auth")
		{
			auth.POST("/register", authController.Register)
			auth.POST("/login", authController.Login)
			auth.POST("/wechat-login", authController.WechatLogin)
			auth.POST("/app-login", authController.AppLogin)
		}

		// 积分卡密查询（无需认证）
		public.GET("/point-cards/:code", pointCardController.GetPointCardByCode)
	}

	// 需要认证的路由
	protected := r.Group("/api/v1")
	protected.Use(middleware.AuthMiddleware(cfg.JWT.Secret))
	{
		// 用户相关
		user := protected.Group("/user")
		{
			user.GET("/profile", authController.GetProfile)
			user.PUT("/profile", authController.UpdateProfile)
			user.POST("/refresh-token", authController.RefreshToken)
		}

		// 积分卡密相关
		pointCards := protected.Group("/point-cards")
		{
			pointCards.POST("/use", pointCardController.UsePointCard)
		}

		// 解题相关
		solve := protected.Group("/solve")
		{
			solve.POST("/upload", solveController.UploadImage)
			solve.POST("/problem", solveController.SolveProblem)
			solve.POST("/upload-and-solve", solveController.UploadAndSolve)
			solve.GET("/records", solveController.GetSolveRecords)
			solve.GET("/records/:id", solveController.GetSolveRecord)
			solve.GET("/stats", solveController.GetSolveStats)
		}

		// 代理商申请
		protected.POST("/agent/apply", agentController.ApplyAgent)
	}

	// 代理商路由
	agent := r.Group("/api/v1/agent")
	agent.Use(middleware.AuthMiddleware(cfg.JWT.Secret))
	agent.Use(middleware.AgentMiddleware())
	{
		agent.POST("/point-cards", agentController.GeneratePointCard)
		agent.GET("/point-cards", pointCardController.GetMyPointCards)
		agent.GET("/point-cards/stats", pointCardController.GetMyUsageStats)
		agent.GET("/transactions", agentController.GetAgentTransactions)
	}

	// 管理员路由
	admin := r.Group("/api/v1/admin")
	admin.Use(middleware.AuthMiddleware(cfg.JWT.Secret))
	admin.Use(middleware.AdminMiddleware())
	{
		// 用户管理
		users := admin.Group("/users")
		{
			users.GET("/:id", userController.GetUserByID)
			users.POST("/:id/points", userController.AddUserPoints)
			users.POST("/:id/agent", userController.BecomeAgent)
		}

		// 代理商管理
		agents := admin.Group("/agents")
		{
			agents.GET("/applications", agentController.GetApplications)
			agents.POST("/applications/:id/review", agentController.ReviewApplication)
			agents.POST("/:agent_id/balance", agentController.AddAgentBalance)
		}

		// 积分卡密管理
		pointCards := admin.Group("/point-cards")
		{
			pointCards.POST("", pointCardController.GeneratePointCard)
			pointCards.GET("", pointCardController.GetPointCards)
			pointCards.GET("/stats", pointCardController.GetUsageStats)
			pointCards.DELETE("/:id", pointCardController.DisablePointCard)
		}

		// 解题记录管理
		admin.GET("/solve/records", solveController.GetAllSolveRecords)
	}

	// 健康检查
	r.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status": "ok",
			"message": "Solve API is running",
		})
	})

	return r
}

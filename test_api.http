### 健康检查
GET http://localhost:8080/health

### 用户注册
POST http://localhost:8080/api/v1/auth/register
Content-Type: application/json

{
  "username": "testuser",
  "password": "123456",
  "nickname": "测试用户"
}

### 用户登录
POST http://localhost:8080/api/v1/auth/login
Content-Type: application/json

{
  "username": "testuser",
  "password": "123456"
}

### 管理员登录
POST http://localhost:8080/api/v1/auth/login
Content-Type: application/json

{
  "username": "admin",
  "password": "admin123"
}

### 获取用户信息
GET http://localhost:8080/api/v1/user/profile
Authorization: Bearer YOUR_TOKEN_HERE

### 微信登录
POST http://localhost:8080/api/v1/auth/wechat-login
Content-Type: application/json

{
  "open_id": "wx_test_openid_123",
  "nickname": "微信用户",
  "avatar": "https://example.com/avatar.jpg"
}

### App登录
POST http://localhost:8080/api/v1/auth/app-login
Content-Type: application/json

{
  "app_user_id": "app_test_user_123",
  "nickname": "App用户",
  "avatar": "https://example.com/avatar.jpg"
}

### 申请成为代理商
POST http://localhost:8080/api/v1/agent/apply
Authorization: Bearer YOUR_TOKEN_HERE
Content-Type: application/json

{
  "reason": "我想成为代理商，帮助推广产品"
}

### 获取代理商申请列表（管理员）
GET http://localhost:8080/api/v1/admin/agents/applications?status=0
Authorization: Bearer ADMIN_TOKEN_HERE

### 审核代理商申请（管理员）
POST http://localhost:8080/api/v1/admin/agents/applications/1/review
Authorization: Bearer ADMIN_TOKEN_HERE
Content-Type: application/json

{
  "approved": true,
  "note": "申请通过"
}

### 管理员生成积分卡密
POST http://localhost:8080/api/v1/admin/point-cards
Authorization: Bearer ADMIN_TOKEN_HERE
Content-Type: application/json

{
  "points": 100,
  "expire_days": 30
}

### 代理商生成积分卡密
POST http://localhost:8080/api/v1/agent/point-cards
Authorization: Bearer AGENT_TOKEN_HERE
Content-Type: application/json

{
  "points": 50,
  "price": 500,
  "expire_days": 15
}

### 使用积分卡密
POST http://localhost:8080/api/v1/point-cards/use
Authorization: Bearer USER_TOKEN_HERE
Content-Type: application/json

{
  "code": "XXXX-XXXX-XXXX-XXXX"
}

### 查询卡密信息
GET http://localhost:8080/api/v1/point-cards/XXXX-XXXX-XXXX-XXXX

### 上传图片
POST http://localhost:8080/api/v1/solve/upload
Authorization: Bearer USER_TOKEN_HERE
Content-Type: multipart/form-data

### 解题
POST http://localhost:8080/api/v1/solve/problem
Authorization: Bearer USER_TOKEN_HERE
Content-Type: application/json

{
  "image_url": "https://example.com/problem.jpg"
}

### 获取解题记录
GET http://localhost:8080/api/v1/solve/records?page=1&page_size=10
Authorization: Bearer USER_TOKEN_HERE

### 获取解题统计
GET http://localhost:8080/api/v1/solve/stats
Authorization: Bearer USER_TOKEN_HERE

### 增加用户积分（管理员）
POST http://localhost:8080/api/v1/admin/users/2/points
Authorization: Bearer ADMIN_TOKEN_HERE
Content-Type: application/json

{
  "points": 100,
  "description": "管理员充值"
}

### 增加代理商余额（管理员）
POST http://localhost:8080/api/v1/admin/agents/2/balance
Authorization: Bearer ADMIN_TOKEN_HERE
Content-Type: application/json

{
  "amount": 10000,
  "description": "代理商余额充值"
}

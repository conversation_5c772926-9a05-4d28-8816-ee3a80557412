package main

import (
	"log"
	"solve-api/config"
	"solve-api/database"
	"solve-api/router"
)

func main() {
	// 加载配置
	cfg := config.Load()

	// 初始化数据库
	db, err := database.InitMySQL(cfg.MySQL)
	if err != nil {
		log.Fatal("Failed to connect to MySQL:", err)
	}

	// 初始化Redis
	rdb, err := database.InitRedis(cfg.Redis)
	if err != nil {
		log.Fatal("Failed to connect to Redis:", err)
	}

	// 自动迁移数据库表
	if err := database.AutoMigrate(db); err != nil {
		log.Fatal("Failed to migrate database:", err)
	}

	// 初始化路由
	r := router.SetupRouter(db, rdb, cfg)

	// 启动服务器
	log.Printf("Server starting on port %s", cfg.Server.Port)
	if err := r.Run(":" + cfg.Server.Port); err != nil {
		log.Fatal("Failed to start server:", err)
	}
}

使用用go开发一款拍照搜题的后端api服务，gin框架；mysql8与redis；

面向对象实现功能，让我们先完成一个mvp版本。后期会对相关业务进行扩展开发；

1. 用户注册与登录；
    - 小程序端使用
    - App端使用
    - 需要处理双端账号打通问题

2. 用户信息系统
    - 用户头像
    - 用户昵称
    - 用户余额
    - 用户积分

3. 代理商信息系统
    - 代理商头像
    - 代理商昵称
    - 代理商余额
    - 代理商是用户通过申请，管理员审核通过后成为代理商的；
    - 代理商就是用户本身，但是需要申请才能获得代理商身份
    - 代理商可以使用余额来兑换积分卡密，代理商生成的积分卡密其他用户可以使用激活



4. 积分卡密系统
    - 管理员用户可以生成积分卡密
    - 代理商可以使用自己的余额生成积分卡密
    - 用户可以使用积分卡密兑换积分
    - 积分卡密的使用情况

5. 解题模块系统
    - 用户上传图片保存至阿里云oss，并获得图片url
    - 将url请求提交给三方api
    - 将api返回的结果响应给用户
    - 正常返回扣除用户积分
    - 异常返回不扣除用户积分

6. 解题记录系统
    - 用户请求的图片url地址
    - 三方api返回的结果

7. 用户积分与余额记录系统
    - 用户每次请求扣除的积分记录
    - 用户每次充值的记录
    - 用户每次使用积分卡密兑换的记录

8. 代理商积分与余额记录系统
    - 代理商每次生成积分卡密的记录
    - 代理商每次兑换积分卡密的记录


    

## 数据库信息

MySQL_8数据库
MYSQL_HOST=***********
MYSQL_PORT=3380
MYSQL_USERNAME=gmdns
MYSQL_PASSWORD=Suyan15913..
MYSQL_DATABASE=solve_web
MYSQL_CHARSET=utf8mb4

## oss配置信息；

AccessKey ID
LTAI5tMWNMyAdqnpghJf51jy

AccessKey Secret
******************************

Bucket
bonuspoints

Bucket域名
bonuspoints.oss-cn-hangzhou.aliyuncs.com

指定上传目录
/solve/

指定图片命名
solve_当前时间.jpg

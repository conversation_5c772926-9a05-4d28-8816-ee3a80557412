package services

import (
	"fmt"
	"io"
	"mime/multipart"
	"path/filepath"
	"solve-api/config"
	"strings"
	"time"

	"github.com/aliyun/aliyun-oss-go-sdk/oss"
)

type UploadService struct {
	config *config.Config
	client *oss.Client
	bucket *oss.Bucket
}

func NewUploadService(cfg *config.Config) (*UploadService, error) {
	client, err := oss.New(cfg.OSS.Endpoint, cfg.OSS.AccessKeyID, cfg.OSS.AccessKeySecret)
	if err != nil {
		return nil, err
	}

	bucket, err := client.Bucket(cfg.OSS.BucketName)
	if err != nil {
		return nil, err
	}

	return &UploadService{
		config: cfg,
		client: client,
		bucket: bucket,
	}, nil
}

// UploadImage 上传图片到OSS
func (s *UploadService) UploadImage(file *multipart.FileHeader, userID uint) (string, error) {
	// 检查文件类型
	if !s.isValidImageType(file.Filename) {
		return "", fmt.Errorf("不支持的文件类型")
	}

	// 检查文件大小 (限制为10MB)
	if file.Size > 10*1024*1024 {
		return "", fmt.Errorf("文件大小超过限制")
	}

	// 打开文件
	src, err := file.Open()
	if err != nil {
		return "", err
	}
	defer src.Close()

	// 生成文件名
	filename := s.generateFilename(file.Filename, userID)

	// 上传到OSS
	err = s.bucket.PutObject(filename, src)
	if err != nil {
		return "", err
	}

	// 返回文件URL
	url := fmt.Sprintf("https://%s.%s/%s", s.config.OSS.BucketName, 
		strings.TrimPrefix(s.config.OSS.Endpoint, "https://"), filename)

	return url, nil
}

// UploadFromReader 从Reader上传文件
func (s *UploadService) UploadFromReader(reader io.Reader, filename string, userID uint) (string, error) {
	// 生成文件名
	objectName := s.generateFilename(filename, userID)

	// 上传到OSS
	err := s.bucket.PutObject(objectName, reader)
	if err != nil {
		return "", err
	}

	// 返回文件URL
	url := fmt.Sprintf("https://%s.%s/%s", s.config.OSS.BucketName, 
		strings.TrimPrefix(s.config.OSS.Endpoint, "https://"), objectName)

	return url, nil
}

// DeleteFile 删除文件
func (s *UploadService) DeleteFile(filename string) error {
	return s.bucket.DeleteObject(filename)
}

// GetSignedURL 获取签名URL
func (s *UploadService) GetSignedURL(filename string, expireTime time.Duration) (string, error) {
	return s.bucket.SignURL(filename, oss.HTTPGet, int64(expireTime.Seconds()))
}

// isValidImageType 检查是否为有效的图片类型
func (s *UploadService) isValidImageType(filename string) bool {
	ext := strings.ToLower(filepath.Ext(filename))
	validExts := []string{".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp"}
	
	for _, validExt := range validExts {
		if ext == validExt {
			return true
		}
	}
	return false
}

// generateFilename 生成文件名
func (s *UploadService) generateFilename(originalFilename string, userID uint) string {
	ext := filepath.Ext(originalFilename)
	timestamp := time.Now().Unix()
	
	// 格式: images/user_{userID}/{timestamp}_{random}{ext}
	return fmt.Sprintf("images/user_%d/%d_%s%s", 
		userID, timestamp, generateRandomString(8), ext)
}

// generateRandomString 生成随机字符串
func generateRandomString(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyz0123456789"
	b := make([]byte, length)
	for i := range b {
		b[i] = charset[time.Now().UnixNano()%int64(len(charset))]
	}
	return string(b)
}

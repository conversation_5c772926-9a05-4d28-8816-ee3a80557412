package services

import (
	"fmt"
	"io"
	"mime/multipart"
	"os"
	"path/filepath"
	"solve-api/config"
	"time"
)

type UploadService struct {
	config *config.Config
}

func NewUploadService(cfg *config.Config) (*UploadService, error) {
	// 创建上传目录
	uploadDir := "uploads/solve"
	if err := os.MkdirAll(uploadDir, 0755); err != nil {
		return nil, err
	}

	return &UploadService{
		config: cfg,
	}, nil
}

// UploadImage 上传图片到本地（模拟OSS）
func (s *UploadService) UploadImage(file *multipart.FileHeader, userID uint) (string, error) {
	// 检查文件类型
	if !s.isValidImageType(file.Filename) {
		return "", fmt.Errorf("不支持的文件类型")
	}

	// 检查文件大小 (限制为10MB)
	if file.Size > 10*1024*1024 {
		return "", fmt.Errorf("文件大小超过限制")
	}

	// 打开文件
	src, err := file.Open()
	if err != nil {
		return "", err
	}
	defer src.Close()

	// 生成文件名（固定为.jpg格式）
	filename := s.generateFilename(file.Filename, userID)
	filepath := fmt.Sprintf("uploads/%s", filename)

	// 创建目标文件
	dst, err := os.Create(filepath)
	if err != nil {
		return "", err
	}
	defer dst.Close()

	// 复制文件内容
	_, err = io.Copy(dst, src)
	if err != nil {
		return "", err
	}

	// 返回文件URL - 模拟OSS域名
	url := fmt.Sprintf("https://bonuspoints.oss-cn-hangzhou.aliyuncs.com/%s", filename)

	return url, nil
}

// UploadFromReader 从Reader上传文件
func (s *UploadService) UploadFromReader(reader io.Reader, filename string, userID uint) (string, error) {
	// 生成文件名
	objectName := s.generateFilename(filename, userID)
	filepath := fmt.Sprintf("uploads/%s", objectName)

	// 创建目标文件
	dst, err := os.Create(filepath)
	if err != nil {
		return "", err
	}
	defer dst.Close()

	// 复制文件内容
	_, err = io.Copy(dst, reader)
	if err != nil {
		return "", err
	}

	// 返回文件URL - 模拟OSS域名
	url := fmt.Sprintf("https://bonuspoints.oss-cn-hangzhou.aliyuncs.com/%s", objectName)

	return url, nil
}

// DeleteFile 删除文件
func (s *UploadService) DeleteFile(filename string) error {
	filepath := fmt.Sprintf("uploads/%s", filename)
	return os.Remove(filepath)
}

// GetSignedURL 获取签名URL（模拟）
func (s *UploadService) GetSignedURL(filename string, expireTime time.Duration) (string, error) {
	url := fmt.Sprintf("https://bonuspoints.oss-cn-hangzhou.aliyuncs.com/%s", filename)
	return url, nil
}

// isValidImageType 检查是否为有效的图片类型
func (s *UploadService) isValidImageType(filename string) bool {
	ext := strings.ToLower(filepath.Ext(filename))
	validExts := []string{".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp"}
	
	for _, validExt := range validExts {
		if ext == validExt {
			return true
		}
	}
	return false
}

// generateFilename 生成文件名
func (s *UploadService) generateFilename(originalFilename string, userID uint) string {
	// 按照要求：指定上传目录 /solve/，指定图片命名 solve_当前时间.jpg
	timestamp := time.Now().Format("20060102150405") // 格式：YYYYMMDDHHMMSS

	// 格式: solve/solve_{timestamp}.jpg
	return fmt.Sprintf("solve/solve_%s.jpg", timestamp)
}

// generateRandomString 生成随机字符串
func generateRandomString(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyz0123456789"
	b := make([]byte, length)
	for i := range b {
		b[i] = charset[time.Now().UnixNano()%int64(len(charset))]
	}
	return string(b)
}

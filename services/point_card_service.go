package services

import (
	"errors"
	"solve-api/models"
	"solve-api/utils"
	"time"

	"gorm.io/gorm"
)

type PointCardService struct {
	db *gorm.DB
}

func NewPointCardService(db *gorm.DB) *PointCardService {
	return &PointCardService{db: db}
}

// GeneratePointCard 管理员生成积分卡密
func (s *PointCardService) GeneratePointCard(adminID uint, points int64, expireDays int) (*models.PointCard, error) {
	code := utils.GeneratePointCardCode()
	var expireAt time.Time
	if expireDays > 0 {
		expireAt = time.Now().AddDate(0, 0, expireDays)
	}

	pointCard := &models.PointCard{
		Code:        code,
		Points:      points,
		Price:       0, // 管理员生成的卡密价格为0
		CreatorID:   adminID,
		CreatorType: models.PointCardCreatorTypeAdmin,
		ExpireAt:    expireAt,
		Status:      1,
	}

	if err := s.db.Create(pointCard).Error; err != nil {
		return nil, err
	}

	return pointCard, nil
}

// UsePointCard 使用积分卡密
func (s *PointCardService) UsePointCard(userID uint, code string) error {
	return s.db.Transaction(func(tx *gorm.DB) error {
		// 查找卡密
		var pointCard models.PointCard
		if err := tx.Where("code = ?", code).First(&pointCard).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return errors.New("卡密不存在")
			}
			return err
		}

		// 检查卡密是否有效
		if !pointCard.IsValid() {
			if pointCard.IsUsed {
				return errors.New("卡密已被使用")
			}
			if pointCard.IsExpired() {
				return errors.New("卡密已过期")
			}
			return errors.New("卡密无效")
		}

		// 使用卡密
		pointCard.Use(userID)
		if err := tx.Save(&pointCard).Error; err != nil {
			return err
		}

		// 获取用户信息
		var user models.User
		if err := tx.First(&user, userID).Error; err != nil {
			return err
		}

		// 增加用户积分
		pointsBefore := user.Points
		user.AddPoints(pointCard.Points)
		if err := tx.Save(&user).Error; err != nil {
			return err
		}

		// 记录用户交易
		transaction := &models.UserTransaction{
			UserID:        userID,
			Type:          models.UserTransactionTypeCardRedeem,
			Amount:        pointCard.Points,
			Points:        pointCard.Points,
			Description:   "使用积分卡密",
			RelatedID:     pointCard.ID,
			RelatedType:   "point_card",
			PointsBefore:  pointsBefore,
			PointsAfter:   user.Points,
			BalanceBefore: user.Balance,
			BalanceAfter:  user.Balance,
			Status:        1,
		}

		return tx.Create(transaction).Error
	})
}

// GetPointCards 获取积分卡密列表
func (s *PointCardService) GetPointCards(creatorID uint, creatorType int, status int, page, pageSize int) ([]models.PointCard, int64, error) {
	var pointCards []models.PointCard
	var total int64

	query := s.db.Model(&models.PointCard{}).Preload("Creator").Preload("User")

	if creatorID > 0 {
		query = query.Where("creator_id = ?", creatorID)
	}
	if creatorType > 0 {
		query = query.Where("creator_type = ?", creatorType)
	}
	if status >= 0 {
		query = query.Where("status = ?", status)
	}

	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	offset := (page - 1) * pageSize
	if err := query.Offset(offset).Limit(pageSize).Order("created_at DESC").Find(&pointCards).Error; err != nil {
		return nil, 0, err
	}

	return pointCards, total, nil
}

// GetPointCardByCode 根据卡密获取信息
func (s *PointCardService) GetPointCardByCode(code string) (*models.PointCard, error) {
	var pointCard models.PointCard
	if err := s.db.Preload("Creator").Preload("User").Where("code = ?", code).First(&pointCard).Error; err != nil {
		return nil, err
	}
	return &pointCard, nil
}

// DisablePointCard 禁用积分卡密
func (s *PointCardService) DisablePointCard(cardID uint) error {
	return s.db.Model(&models.PointCard{}).Where("id = ?", cardID).Update("status", 0).Error
}

// GetUsageStats 获取卡密使用统计
func (s *PointCardService) GetUsageStats(creatorID uint, creatorType int) (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	query := s.db.Model(&models.PointCard{})
	if creatorID > 0 {
		query = query.Where("creator_id = ?", creatorID)
	}
	if creatorType > 0 {
		query = query.Where("creator_type = ?", creatorType)
	}

	// 总卡密数
	var totalCount int64
	query.Count(&totalCount)
	stats["total_count"] = totalCount

	// 已使用数量
	var usedCount int64
	query.Where("is_used = ?", true).Count(&usedCount)
	stats["used_count"] = usedCount

	// 未使用数量
	stats["unused_count"] = totalCount - usedCount

	// 已过期数量
	var expiredCount int64
	query.Where("expire_at < ? AND expire_at != ?", time.Now(), time.Time{}).Count(&expiredCount)
	stats["expired_count"] = expiredCount

	// 总积分数
	var totalPoints int64
	query.Select("COALESCE(SUM(points), 0)").Scan(&totalPoints)
	stats["total_points"] = totalPoints

	// 已使用积分数
	var usedPoints int64
	query.Where("is_used = ?", true).Select("COALESCE(SUM(points), 0)").Scan(&usedPoints)
	stats["used_points"] = usedPoints

	return stats, nil
}

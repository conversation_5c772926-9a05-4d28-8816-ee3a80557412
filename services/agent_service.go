package services

import (
	"errors"
	"solve-api/models"
	"solve-api/utils"
	"time"

	"gorm.io/gorm"
)

type AgentService struct {
	db *gorm.DB
}

func NewAgentService(db *gorm.DB) *AgentService {
	return &AgentService{db: db}
}

// ApplyAgent 申请成为代理商
func (s *AgentService) ApplyAgent(userID uint, reason string) error {
	// 检查用户是否已经是代理商
	var user models.User
	if err := s.db.First(&user, userID).Error; err != nil {
		return err
	}

	if user.IsAgent {
		return errors.New("用户已经是代理商")
	}

	// 检查是否已有待审核的申请
	var existApp models.AgentApplication
	if err := s.db.Where("user_id = ? AND status = ?", userID, models.AgentApplicationStatusPending).First(&existApp).Error; err == nil {
		return errors.New("已有待审核的申请")
	}

	// 创建申请
	application := &models.AgentApplication{
		UserID: userID,
		Reason: reason,
		Status: models.AgentApplicationStatusPending,
	}

	return s.db.Create(application).Error
}

// GetApplications 获取代理商申请列表
func (s *AgentService) GetApplications(status int, page, pageSize int) ([]models.AgentApplication, int64, error) {
	var applications []models.AgentApplication
	var total int64

	query := s.db.Model(&models.AgentApplication{}).Preload("User")
	if status >= 0 {
		query = query.Where("status = ?", status)
	}

	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	offset := (page - 1) * pageSize
	if err := query.Offset(offset).Limit(pageSize).Order("created_at DESC").Find(&applications).Error; err != nil {
		return nil, 0, err
	}

	return applications, total, nil
}

// ReviewApplication 审核代理商申请
func (s *AgentService) ReviewApplication(applicationID, reviewerID uint, approved bool, note string) error {
	return s.db.Transaction(func(tx *gorm.DB) error {
		var application models.AgentApplication
		if err := tx.Preload("User").First(&application, applicationID).Error; err != nil {
			return err
		}

		if !application.IsPending() {
			return errors.New("申请已被处理")
		}

		if approved {
			application.Approve(reviewerID, note)
			
			// 更新用户为代理商
			agentCode := utils.GenerateAgentCode()
			if err := tx.Model(&models.User{}).Where("id = ?", application.UserID).Updates(map[string]interface{}{
				"is_agent":   true,
				"agent_at":   time.Now(),
				"agent_code": agentCode,
			}).Error; err != nil {
				return err
			}
		} else {
			application.Reject(reviewerID, note)
		}

		return tx.Save(&application).Error
	})
}

// GeneratePointCard 代理商生成积分卡密
func (s *AgentService) GeneratePointCard(agentID uint, points, price int64, expireDays int) (*models.PointCard, error) {
	return s.db.Transaction(func(tx *gorm.DB) error {
		// 检查代理商余额
		var agent models.User
		if err := tx.First(&agent, agentID).Error; err != nil {
			return err
		}

		if !agent.IsAgent {
			return errors.New("用户不是代理商")
		}

		if agent.Balance < price {
			return errors.New("余额不足")
		}

		// 扣除余额
		balanceBefore := agent.Balance
		agent.DeductBalance(price)
		if err := tx.Save(&agent).Error; err != nil {
			return err
		}

		// 生成卡密
		code := utils.GeneratePointCardCode()
		var expireAt time.Time
		if expireDays > 0 {
			expireAt = time.Now().AddDate(0, 0, expireDays)
		}

		pointCard := &models.PointCard{
			Code:        code,
			Points:      points,
			Price:       price,
			CreatorID:   agentID,
			CreatorType: models.PointCardCreatorTypeAgent,
			ExpireAt:    expireAt,
			Status:      1,
		}

		if err := tx.Create(pointCard).Error; err != nil {
			return err
		}

		// 记录代理商交易
		transaction := &models.AgentTransaction{
			AgentID:       agentID,
			Type:          models.AgentTransactionTypeGenerateCard,
			Amount:        price,
			Points:        points,
			Description:   "生成积分卡密",
			RelatedID:     pointCard.ID,
			RelatedType:   "point_card",
			BalanceBefore: balanceBefore,
			BalanceAfter:  agent.Balance,
			Status:        1,
		}

		if err := tx.Create(transaction).Error; err != nil {
			return err
		}

		return nil
	})
}

// GetAgentTransactions 获取代理商交易记录
func (s *AgentService) GetAgentTransactions(agentID uint, page, pageSize int) ([]models.AgentTransaction, int64, error) {
	var transactions []models.AgentTransaction
	var total int64

	query := s.db.Model(&models.AgentTransaction{}).Where("agent_id = ?", agentID)

	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	offset := (page - 1) * pageSize
	if err := query.Offset(offset).Limit(pageSize).Order("created_at DESC").Find(&transactions).Error; err != nil {
		return nil, 0, err
	}

	return transactions, total, nil
}

// AddAgentBalance 增加代理商余额
func (s *AgentService) AddAgentBalance(agentID uint, amount int64, description string) error {
	return s.db.Transaction(func(tx *gorm.DB) error {
		var agent models.User
		if err := tx.First(&agent, agentID).Error; err != nil {
			return err
		}

		if !agent.IsAgent {
			return errors.New("用户不是代理商")
		}

		balanceBefore := agent.Balance
		agent.AddBalance(amount)

		if err := tx.Save(&agent).Error; err != nil {
			return err
		}

		// 记录交易
		transaction := &models.AgentTransaction{
			AgentID:       agentID,
			Type:          models.AgentTransactionTypeBalanceRecharge,
			Amount:        amount,
			Description:   description,
			BalanceBefore: balanceBefore,
			BalanceAfter:  agent.Balance,
			Status:        1,
		}

		return tx.Create(transaction).Error
	})
}

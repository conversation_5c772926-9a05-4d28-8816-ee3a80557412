package services

import (
	"errors"
	"solve-api/models"
	"solve-api/utils"
	"time"

	"gorm.io/gorm"
)

type UserService struct {
	db *gorm.DB
}

func NewUserService(db *gorm.DB) *UserService {
	return &UserService{db: db}
}

// Register 用户注册
func (s *UserService) Register(username, password, nickname string) (*models.User, error) {
	// 检查用户名是否已存在
	var existUser models.User
	if err := s.db.Where("username = ?", username).First(&existUser).Error; err == nil {
		return nil, errors.New("用户名已存在")
	}

	// 加密密码
	hashedPassword, err := utils.HashPassword(password)
	if err != nil {
		return nil, err
	}

	// 创建用户
	user := &models.User{
		Username: username,
		Password: hashedPassword,
		Nickname: nickname,
		Status:   1,
	}

	if err := s.db.Create(user).Error; err != nil {
		return nil, err
	}

	return user, nil
}

// Login 用户登录
func (s *UserService) Login(username, password string) (*models.User, error) {
	var user models.User
	if err := s.db.Where("username = ? AND status = 1", username).First(&user).Error; err != nil {
		return nil, errors.New("用户不存在或已被禁用")
	}

	if !utils.CheckPassword(password, user.Password) {
		return nil, errors.New("密码错误")
	}

	return &user, nil
}

// WechatLogin 微信登录
func (s *UserService) WechatLogin(openID, nickname, avatar string) (*models.User, error) {
	var user models.User
	
	// 查找是否已存在该openID的用户
	if err := s.db.Where("wechat_open_id = ?", openID).First(&user).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 创建新用户
			username := utils.GenerateUsername("wx")
			user = models.User{
				Username:     username,
				Password:     utils.GenerateCode(16), // 随机密码
				Nickname:     nickname,
				Avatar:       avatar,
				WechatOpenID: openID,
				Status:       1,
			}
			if err := s.db.Create(&user).Error; err != nil {
				return nil, err
			}
		} else {
			return nil, err
		}
	}

	return &user, nil
}

// AppLogin App登录
func (s *UserService) AppLogin(appUserID, nickname, avatar string) (*models.User, error) {
	var user models.User
	
	// 查找是否已存在该appUserID的用户
	if err := s.db.Where("app_user_id = ?", appUserID).First(&user).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 创建新用户
			username := utils.GenerateUsername("app")
			user = models.User{
				Username:  username,
				Password:  utils.GenerateCode(16), // 随机密码
				Nickname:  nickname,
				Avatar:    avatar,
				AppUserID: appUserID,
				Status:    1,
			}
			if err := s.db.Create(&user).Error; err != nil {
				return nil, err
			}
		} else {
			return nil, err
		}
	}

	return &user, nil
}

// GetUserByID 根据ID获取用户
func (s *UserService) GetUserByID(id uint) (*models.User, error) {
	var user models.User
	if err := s.db.First(&user, id).Error; err != nil {
		return nil, err
	}
	return &user, nil
}

// UpdateUser 更新用户信息
func (s *UserService) UpdateUser(userID uint, nickname, avatar string) error {
	return s.db.Model(&models.User{}).Where("id = ?", userID).Updates(map[string]interface{}{
		"nickname": nickname,
		"avatar":   avatar,
	}).Error
}

// BecomeAgent 成为代理商
func (s *UserService) BecomeAgent(userID uint) error {
	agentCode := utils.GenerateAgentCode()
	return s.db.Model(&models.User{}).Where("id = ?", userID).Updates(map[string]interface{}{
		"is_agent":   true,
		"agent_at":   time.Now(),
		"agent_code": agentCode,
	}).Error
}

// AddPoints 增加积分
func (s *UserService) AddPoints(userID uint, points int64, description string) error {
	return s.db.Transaction(func(tx *gorm.DB) error {
		var user models.User
		if err := tx.First(&user, userID).Error; err != nil {
			return err
		}

		pointsBefore := user.Points
		user.AddPoints(points)

		if err := tx.Save(&user).Error; err != nil {
			return err
		}

		// 记录交易
		transaction := &models.UserTransaction{
			UserID:        userID,
			Type:          models.UserTransactionTypePointsRecharge,
			Amount:        points,
			Points:        points,
			Description:   description,
			PointsBefore:  pointsBefore,
			PointsAfter:   user.Points,
			BalanceBefore: user.Balance,
			BalanceAfter:  user.Balance,
			Status:        1,
		}

		return tx.Create(transaction).Error
	})
}

// DeductPoints 扣除积分
func (s *UserService) DeductPoints(userID uint, points int64, description string) error {
	return s.db.Transaction(func(tx *gorm.DB) error {
		var user models.User
		if err := tx.First(&user, userID).Error; err != nil {
			return err
		}

		if user.Points < points {
			return errors.New("积分不足")
		}

		pointsBefore := user.Points
		user.DeductPoints(points)

		if err := tx.Save(&user).Error; err != nil {
			return err
		}

		// 记录交易
		transaction := &models.UserTransaction{
			UserID:        userID,
			Type:          models.UserTransactionTypePointsConsume,
			Amount:        points,
			Points:        -points,
			Description:   description,
			PointsBefore:  pointsBefore,
			PointsAfter:   user.Points,
			BalanceBefore: user.Balance,
			BalanceAfter:  user.Balance,
			Status:        1,
		}

		return tx.Create(transaction).Error
	})
}

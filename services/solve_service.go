package services

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"solve-api/config"
	"solve-api/models"
	"time"

	"gorm.io/gorm"
)

type SolveService struct {
	db     *gorm.DB
	config *config.Config
}

func NewSolveService(db *gorm.DB, cfg *config.Config) *SolveService {
	return &SolveService{
		db:     db,
		config: cfg,
	}
}

// SolveRequest 解题请求结构
type SolveRequest struct {
	ImageURL string `json:"image_url"`
	UserID   uint   `json:"user_id"`
}

// SolveResponse 解题响应结构
type SolveResponse struct {
	Success bool        `json:"success"`
	Data    interface{} `json:"data"`
	Error   string      `json:"error"`
}

// ThirdAPIRequest 第三方API请求结构
type ThirdAPIRequest struct {
	ImageURL string `json:"image_url"`
	APIKey   string `json:"api_key"`
}

// ThirdAPIResponse 第三方API响应结构
type ThirdAPIResponse struct {
	RequestID string      `json:"request_id"`
	Success   bool        `json:"success"`
	Data      interface{} `json:"data"`
	Error     string      `json:"error"`
}

// SolveProblem 解题
func (s *SolveService) SolveProblem(userID uint, imageURL string) (*models.SolveRecord, error) {
	// 检查用户积分
	var user models.User
	if err := s.db.First(&user, userID).Error; err != nil {
		return nil, err
	}

	pointsCost := int64(10) // 假设每次解题消耗10积分
	if !user.CanSolve(pointsCost) {
		return nil, errors.New("积分不足或用户状态异常")
	}

	// 创建解题记录
	record := &models.SolveRecord{
		UserID:     userID,
		ImageURL:   imageURL,
		PointsCost: pointsCost,
		Status:     1,
	}

	if err := s.db.Create(record).Error; err != nil {
		return nil, err
	}

	// 调用第三方API
	startTime := time.Now()
	response, err := s.callThirdAPI(imageURL)
	latency := time.Since(startTime).Milliseconds()

	if err != nil {
		// API调用失败，不扣除积分
		record.MarkFailed(err.Error())
		record.SetThirdAPIInfo("", latency)
		s.db.Save(record)
		return record, err
	}

	// API调用成功
	if response.Success {
		// 扣除用户积分
		if err := s.deductUserPoints(userID, pointsCost, record.ID); err != nil {
			record.MarkFailed("扣除积分失败: " + err.Error())
		} else {
			responseData, _ := json.Marshal(response.Data)
			record.MarkSuccess(string(responseData))
			record.MarkDeducted(pointsCost)
		}
	} else {
		// API返回错误，不扣除积分
		record.MarkFailed(response.Error)
	}

	record.SetThirdAPIInfo(response.RequestID, latency)
	s.db.Save(record)

	return record, nil
}

// callThirdAPI 调用第三方解题API
func (s *SolveService) callThirdAPI(imageURL string) (*ThirdAPIResponse, error) {
	request := ThirdAPIRequest{
		ImageURL: imageURL,
		APIKey:   s.config.ThirdAPI.APIKey,
	}

	requestData, err := json.Marshal(request)
	if err != nil {
		return nil, err
	}

	resp, err := http.Post(s.config.ThirdAPI.SolveURL, "application/json", bytes.NewBuffer(requestData))
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API请求失败，状态码: %d", resp.StatusCode)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	var response ThirdAPIResponse
	if err := json.Unmarshal(body, &response); err != nil {
		return nil, err
	}

	return &response, nil
}

// deductUserPoints 扣除用户积分
func (s *SolveService) deductUserPoints(userID uint, points int64, recordID uint) error {
	return s.db.Transaction(func(tx *gorm.DB) error {
		var user models.User
		if err := tx.First(&user, userID).Error; err != nil {
			return err
		}

		if user.Points < points {
			return errors.New("积分不足")
		}

		pointsBefore := user.Points
		user.DeductPoints(points)

		if err := tx.Save(&user).Error; err != nil {
			return err
		}

		// 记录交易
		transaction := &models.UserTransaction{
			UserID:        userID,
			Type:          models.UserTransactionTypePointsConsume,
			Amount:        points,
			Points:        -points,
			Description:   "解题消费",
			RelatedID:     recordID,
			RelatedType:   "solve_record",
			PointsBefore:  pointsBefore,
			PointsAfter:   user.Points,
			BalanceBefore: user.Balance,
			BalanceAfter:  user.Balance,
			Status:        1,
		}

		return tx.Create(transaction).Error
	})
}

// GetSolveRecords 获取解题记录
func (s *SolveService) GetSolveRecords(userID uint, page, pageSize int) ([]models.SolveRecord, int64, error) {
	var records []models.SolveRecord
	var total int64

	query := s.db.Model(&models.SolveRecord{}).Where("user_id = ?", userID)

	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	offset := (page - 1) * pageSize
	if err := query.Offset(offset).Limit(pageSize).Order("created_at DESC").Find(&records).Error; err != nil {
		return nil, 0, err
	}

	return records, total, nil
}

// GetSolveRecordByID 根据ID获取解题记录
func (s *SolveService) GetSolveRecordByID(recordID uint) (*models.SolveRecord, error) {
	var record models.SolveRecord
	if err := s.db.Preload("User").First(&record, recordID).Error; err != nil {
		return nil, err
	}
	return &record, nil
}

// GetSolveStats 获取解题统计
func (s *SolveService) GetSolveStats(userID uint) (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	query := s.db.Model(&models.SolveRecord{}).Where("user_id = ?", userID)

	// 总解题次数
	var totalCount int64
	query.Count(&totalCount)
	stats["total_count"] = totalCount

	// 成功次数
	var successCount int64
	query.Where("is_success = ?", true).Count(&successCount)
	stats["success_count"] = successCount

	// 失败次数
	stats["failed_count"] = totalCount - successCount

	// 总消耗积分
	var totalPoints int64
	query.Where("is_deducted = ?", true).Select("COALESCE(SUM(points_cost), 0)").Scan(&totalPoints)
	stats["total_points_cost"] = totalPoints

	// 今日解题次数
	var todayCount int64
	today := time.Now().Format("2006-01-02")
	query.Where("DATE(created_at) = ?", today).Count(&todayCount)
	stats["today_count"] = todayCount

	return stats, nil
}

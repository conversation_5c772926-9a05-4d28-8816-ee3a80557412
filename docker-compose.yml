version: '3.8'

services:
  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: solve-api-mysql
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: solve_api
      MYSQL_USER: solve_user
      MYSQL_PASSWORD: solve_password
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./scripts/init.sql:/docker-entrypoint-initdb.d/init.sql
    command: --default-authentication-plugin=mysql_native_password

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: solve-api-redis
    restart: always
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  # API服务
  api:
    build: .
    container_name: solve-api
    restart: always
    ports:
      - "8080:8080"
    environment:
      - GIN_MODE=release
      - MYSQL_HOST=mysql
      - MYSQL_PASSWORD=solve_password
      - REDIS_HOST=redis
      - JWT_SECRET=your-production-jwt-secret
    depends_on:
      - mysql
      - redis
    volumes:
      - ./config.yaml:/root/config.yaml

volumes:
  mysql_data:
  redis_data:

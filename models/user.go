package models

import (
	"time"

	"gorm.io/gorm"
)

// User 用户模型
type User struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// 基本信息
	Username string `json:"username" gorm:"uniqueIndex;size:50;not null"`
	Password string `json:"-" gorm:"size:255;not null"`
	Nickname string `json:"nickname" gorm:"size:50"`
	Avatar   string `json:"avatar" gorm:"size:255"`

	// 登录相关
	WechatOpenID string `json:"wechat_open_id" gorm:"uniqueIndex;size:100"`
	AppUserID    string `json:"app_user_id" gorm:"uniqueIndex;size:100"`

	// 账户信息
	Balance int64 `json:"balance" gorm:"default:0;comment:余额(分)"`
	Points  int64 `json:"points" gorm:"default:0;comment:积分"`

	// 代理商相关
	IsAgent   bool      `json:"is_agent" gorm:"default:false"`
	AgentAt   time.Time `json:"agent_at,omitempty"`
	AgentCode string    `json:"agent_code" gorm:"uniqueIndex;size:20"`

	// 状态
	Status int `json:"status" gorm:"default:1;comment:1正常 0禁用"`
}

// TableName 指定表名
func (User) TableName() string {
	return "users"
}

// IsValidStatus 检查状态是否有效
func (u *User) IsValidStatus() bool {
	return u.Status == 1
}

// CanSolve 检查是否可以解题
func (u *User) CanSolve(cost int64) bool {
	return u.IsValidStatus() && u.Points >= cost
}

// DeductPoints 扣除积分
func (u *User) DeductPoints(points int64) {
	if u.Points >= points {
		u.Points -= points
	}
}

// AddPoints 增加积分
func (u *User) AddPoints(points int64) {
	u.Points += points
}

// AddBalance 增加余额
func (u *User) AddBalance(balance int64) {
	u.Balance += balance
}

// DeductBalance 扣除余额
func (u *User) DeductBalance(balance int64) {
	if u.Balance >= balance {
		u.Balance -= balance
	}
}

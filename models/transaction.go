package models

import (
	"time"

	"gorm.io/gorm"
)

// UserTransaction 用户交易记录模型
type UserTransaction struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// 用户信息
	UserID uint  `json:"user_id" gorm:"not null;index"`
	User   *User `json:"user,omitempty" gorm:"foreignKey:UserID"`

	// 交易信息
	Type        int    `json:"type" gorm:"not null;comment:1积分消费 2积分充值 3余额充值 4卡密兑换"`
	Amount      int64  `json:"amount" gorm:"not null;comment:金额(分)或积分数"`
	Points      int64  `json:"points" gorm:"default:0;comment:积分变动"`
	Balance     int64  `json:"balance" gorm:"default:0;comment:余额变动(分)"`
	Description string `json:"description" gorm:"size:255;comment:描述"`

	// 关联信息
	RelatedID   uint   `json:"related_id" gorm:"comment:关联ID"`
	RelatedType string `json:"related_type" gorm:"size:50;comment:关联类型"`

	// 余额快照
	BalanceBefore int64 `json:"balance_before" gorm:"comment:交易前余额"`
	BalanceAfter  int64 `json:"balance_after" gorm:"comment:交易后余额"`
	PointsBefore  int64 `json:"points_before" gorm:"comment:交易前积分"`
	PointsAfter   int64 `json:"points_after" gorm:"comment:交易后积分"`

	// 状态
	Status int `json:"status" gorm:"default:1;comment:1成功 0失败"`
}

// TableName 指定表名
func (UserTransaction) TableName() string {
	return "user_transactions"
}

// 交易类型常量
const (
	UserTransactionTypePointsConsume = 1 // 积分消费
	UserTransactionTypePointsRecharge = 2 // 积分充值
	UserTransactionTypeBalanceRecharge = 3 // 余额充值
	UserTransactionTypeCardRedeem = 4 // 卡密兑换
)

// AgentTransaction 代理商交易记录模型
type AgentTransaction struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// 代理商信息
	AgentID uint  `json:"agent_id" gorm:"not null;index"`
	Agent   *User `json:"agent,omitempty" gorm:"foreignKey:AgentID"`

	// 交易信息
	Type        int    `json:"type" gorm:"not null;comment:1生成卡密 2余额充值"`
	Amount      int64  `json:"amount" gorm:"not null;comment:金额(分)"`
	Points      int64  `json:"points" gorm:"default:0;comment:积分数量"`
	Description string `json:"description" gorm:"size:255;comment:描述"`

	// 关联信息
	RelatedID   uint   `json:"related_id" gorm:"comment:关联ID"`
	RelatedType string `json:"related_type" gorm:"size:50;comment:关联类型"`

	// 余额快照
	BalanceBefore int64 `json:"balance_before" gorm:"comment:交易前余额"`
	BalanceAfter  int64 `json:"balance_after" gorm:"comment:交易后余额"`

	// 状态
	Status int `json:"status" gorm:"default:1;comment:1成功 0失败"`
}

// TableName 指定表名
func (AgentTransaction) TableName() string {
	return "agent_transactions"
}

// 代理商交易类型常量
const (
	AgentTransactionTypeGenerateCard = 1 // 生成卡密
	AgentTransactionTypeBalanceRecharge = 2 // 余额充值
)

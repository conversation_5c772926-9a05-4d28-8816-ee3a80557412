package models

import (
	"time"

	"gorm.io/gorm"
)

// AgentApplication 代理商申请模型
type AgentApplication struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	UserID uint   `json:"user_id" gorm:"not null;index"`
	User   *User  `json:"user,omitempty" gorm:"foreignKey:UserID"`
	Reason string `json:"reason" gorm:"type:text;comment:申请理由"`

	// 审核信息
	Status     int       `json:"status" gorm:"default:0;comment:0待审核 1通过 2拒绝"`
	ReviewerID uint      `json:"reviewer_id" gorm:"comment:审核人ID"`
	Reviewer   *User     `json:"reviewer,omitempty" gorm:"foreignKey:ReviewerID"`
	ReviewAt   time.Time `json:"review_at,omitempty"`
	ReviewNote string    `json:"review_note" gorm:"type:text;comment:审核备注"`
}

// TableName 指定表名
func (AgentApplication) TableName() string {
	return "agent_applications"
}

// 申请状态常量
const (
	AgentApplicationStatusPending  = 0 // 待审核
	AgentApplicationStatusApproved = 1 // 通过
	AgentApplicationStatusRejected = 2 // 拒绝
)

// IsPending 是否待审核
func (a *AgentApplication) IsPending() bool {
	return a.Status == AgentApplicationStatusPending
}

// IsApproved 是否已通过
func (a *AgentApplication) IsApproved() bool {
	return a.Status == AgentApplicationStatusApproved
}

// IsRejected 是否已拒绝
func (a *AgentApplication) IsRejected() bool {
	return a.Status == AgentApplicationStatusRejected
}

// Approve 通过申请
func (a *AgentApplication) Approve(reviewerID uint, note string) {
	a.Status = AgentApplicationStatusApproved
	a.ReviewerID = reviewerID
	a.ReviewNote = note
	a.ReviewAt = time.Now()
}

// Reject 拒绝申请
func (a *AgentApplication) Reject(reviewerID uint, note string) {
	a.Status = AgentApplicationStatusRejected
	a.ReviewerID = reviewerID
	a.ReviewNote = note
	a.ReviewAt = time.Now()
}

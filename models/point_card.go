package models

import (
	"time"

	"gorm.io/gorm"
)

// PointCard 积分卡密模型
type PointCard struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// 卡密信息
	Code   string `json:"code" gorm:"uniqueIndex;size:32;not null;comment:卡密"`
	Points int64  `json:"points" gorm:"not null;comment:积分数量"`
	Price  int64  `json:"price" gorm:"not null;comment:价格(分)"`

	// 生成者信息
	CreatorID   uint  `json:"creator_id" gorm:"not null;index;comment:生成者ID"`
	Creator     *User `json:"creator,omitempty" gorm:"foreignKey:CreatorID"`
	CreatorType int   `json:"creator_type" gorm:"not null;comment:1管理员 2代理商"`

	// 使用信息
	IsUsed   bool      `json:"is_used" gorm:"default:false"`
	UserID   uint      `json:"user_id" gorm:"index;comment:使用者ID"`
	User     *User     `json:"user,omitempty" gorm:"foreignKey:UserID"`
	UsedAt   time.Time `json:"used_at,omitempty"`
	ExpireAt time.Time `json:"expire_at" gorm:"comment:过期时间"`

	// 状态
	Status int `json:"status" gorm:"default:1;comment:1有效 0无效"`
}

// TableName 指定表名
func (PointCard) TableName() string {
	return "point_cards"
}

// 生成者类型常量
const (
	PointCardCreatorTypeAdmin = 1 // 管理员
	PointCardCreatorTypeAgent = 2 // 代理商
)

// IsValid 检查卡密是否有效
func (p *PointCard) IsValid() bool {
	now := time.Now()
	return p.Status == 1 && !p.IsUsed && (p.ExpireAt.IsZero() || p.ExpireAt.After(now))
}

// IsExpired 检查是否过期
func (p *PointCard) IsExpired() bool {
	if p.ExpireAt.IsZero() {
		return false
	}
	return time.Now().After(p.ExpireAt)
}

// Use 使用卡密
func (p *PointCard) Use(userID uint) {
	p.IsUsed = true
	p.UserID = userID
	p.UsedAt = time.Now()
}

// Disable 禁用卡密
func (p *PointCard) Disable() {
	p.Status = 0
}

package models

import (
	"time"

	"gorm.io/gorm"
)

// SolveRecord 解题记录模型
type SolveRecord struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// 用户信息
	UserID uint  `json:"user_id" gorm:"not null;index"`
	User   *User `json:"user,omitempty" gorm:"foreignKey:UserID"`

	// 请求信息
	ImageURL    string `json:"image_url" gorm:"size:500;not null;comment:图片URL"`
	RequestData string `json:"request_data" gorm:"type:text;comment:请求数据"`

	// 响应信息
	ResponseData string `json:"response_data" gorm:"type:text;comment:响应数据"`
	IsSuccess    bool   `json:"is_success" gorm:"default:false;comment:是否成功"`
	ErrorMsg     string `json:"error_msg" gorm:"size:500;comment:错误信息"`

	// 消费信息
	PointsCost int64 `json:"points_cost" gorm:"default:0;comment:消耗积分"`
	IsDeducted bool  `json:"is_deducted" gorm:"default:false;comment:是否已扣费"`

	// 第三方API信息
	ThirdAPIRequestID  string `json:"third_api_request_id" gorm:"size:100;comment:第三方API请求ID"`
	ThirdAPIResponseAt time.Time `json:"third_api_response_at,omitempty"`
	ThirdAPILatency    int64     `json:"third_api_latency" gorm:"comment:第三方API延迟(毫秒)"`

	// 状态
	Status int `json:"status" gorm:"default:1;comment:1正常 0异常"`
}

// TableName 指定表名
func (SolveRecord) TableName() string {
	return "solve_records"
}

// MarkSuccess 标记为成功
func (s *SolveRecord) MarkSuccess(responseData string) {
	s.IsSuccess = true
	s.ResponseData = responseData
	s.Status = 1
}

// MarkFailed 标记为失败
func (s *SolveRecord) MarkFailed(errorMsg string) {
	s.IsSuccess = false
	s.ErrorMsg = errorMsg
	s.Status = 0
}

// MarkDeducted 标记已扣费
func (s *SolveRecord) MarkDeducted(pointsCost int64) {
	s.IsDeducted = true
	s.PointsCost = pointsCost
}

// SetThirdAPIInfo 设置第三方API信息
func (s *SolveRecord) SetThirdAPIInfo(requestID string, latency int64) {
	s.ThirdAPIRequestID = requestID
	s.ThirdAPILatency = latency
	s.ThirdAPIResponseAt = time.Now()
}

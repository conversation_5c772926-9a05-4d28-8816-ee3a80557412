package middleware

import (
	"net/http"
	"solve-api/utils"
	"strings"

	"github.com/gin-gonic/gin"
)

// AuthMiddleware JWT认证中间件
func AuthMiddleware(jwtSecret string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取Authorization头
		authHeader := c.<PERSON>("Authorization")
		if authHeader == "" {
			c.J<PERSON>(http.StatusUnauthorized, gin.H{
				"code": 401,
				"msg":  "缺少认证token",
			})
			c.Abort()
			return
		}

		// 检查Bearer前缀
		tokenString := strings.TrimPrefix(authHeader, "Bearer ")
		if tokenString == authHeader {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code": 401,
				"msg":  "token格式错误",
			})
			c.Abort()
			return
		}

		// 验证token
		claims, err := utils.ParseJWT(tokenString, jwtSecret)
		if err != nil {
			c.<PERSON>(http.StatusUnauthorized, gin.H{
				"code": 401,
				"msg":  "token无效",
			})
			c.Abort()
			return
		}

		// 将用户信息存储到上下文
		c.Set("user_id", claims.UserID)
		c.Set("username", claims.Username)
		c.Set("is_agent", claims.IsAgent)

		c.Next()
	}
}

// AgentMiddleware 代理商权限中间件
func AgentMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		isAgent, exists := c.Get("is_agent")
		if !exists || !isAgent.(bool) {
			c.JSON(http.StatusForbidden, gin.H{
				"code": 403,
				"msg":  "需要代理商权限",
			})
			c.Abort()
			return
		}
		c.Next()
	}
}

// AdminMiddleware 管理员权限中间件
func AdminMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 这里可以根据实际需求实现管理员权限检查
		// 比如检查用户角色或特定权限
		userID, exists := c.Get("user_id")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code": 401,
				"msg":  "未认证",
			})
			c.Abort()
			return
		}

		// 简单实现：假设用户ID为1的是管理员
		if userID.(uint) != 1 {
			c.JSON(http.StatusForbidden, gin.H{
				"code": 403,
				"msg":  "需要管理员权限",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

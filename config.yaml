server:
  port: "8080"
  mode: "debug"

mysql:
  host: "localhost"
  port: "3306"
  username: "root"
  password: "password"
  database: "solve_api"
  charset: "utf8mb4"

redis:
  host: "localhost"
  port: "6379"
  password: ""
  db: 0

oss:
  endpoint: "https://oss-cn-hangzhou.aliyuncs.com"
  access_key_id: "your-access-key-id"
  access_key_secret: "your-access-key-secret"
  bucket_name: "your-bucket-name"

third_api:
  solve_url: "https://api.example.com/solve"
  api_key: "your-api-key"

jwt:
  secret: "your-jwt-secret-key"
  expire_time: 7200  # 2小时

package controllers

import (
	"solve-api/services"
	"solve-api/utils"
	"strconv"

	"github.com/gin-gonic/gin"
)

type SolveController struct {
	solveService  *services.SolveService
	uploadService *services.UploadService
}

func NewSolveController(solveService *services.SolveService, uploadService *services.UploadService) *SolveController {
	return &SolveController{
		solveService:  solveService,
		uploadService: uploadService,
	}
}

// UploadAndSolveRequest 上传并解题请求结构
type UploadAndSolveRequest struct {
	ImageURL string `json:"image_url" binding:"required"`
}

// UploadImage 上传图片
func (c *SolveController) UploadImage(ctx *gin.Context) {
	userID, _ := ctx.Get("user_id")

	// 获取上传的文件
	file, err := ctx.FormFile("image")
	if err != nil {
		utils.BadRequest(ctx, "请选择要上传的图片")
		return
	}

	// 上传到OSS
	imageURL, err := c.uploadService.UploadImage(file, userID.(uint))
	if err != nil {
		utils.Error(ctx, 400, "上传失败: "+err.Error())
		return
	}

	utils.Success(ctx, gin.H{
		"image_url": imageURL,
	})
}

// SolveProblem 解题
func (c *SolveController) SolveProblem(ctx *gin.Context) {
	userID, _ := ctx.Get("user_id")

	var req UploadAndSolveRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(ctx, "参数错误: "+err.Error())
		return
	}

	record, err := c.solveService.SolveProblem(userID.(uint), req.ImageURL)
	if err != nil {
		utils.Error(ctx, 400, err.Error())
		return
	}

	utils.Success(ctx, record)
}

// UploadAndSolve 上传图片并解题
func (c *SolveController) UploadAndSolve(ctx *gin.Context) {
	userID, _ := ctx.Get("user_id")

	// 获取上传的文件
	file, err := ctx.FormFile("image")
	if err != nil {
		utils.BadRequest(ctx, "请选择要上传的图片")
		return
	}

	// 上传到OSS
	imageURL, err := c.uploadService.UploadImage(file, userID.(uint))
	if err != nil {
		utils.Error(ctx, 400, "上传失败: "+err.Error())
		return
	}

	// 解题
	record, err := c.solveService.SolveProblem(userID.(uint), imageURL)
	if err != nil {
		utils.Error(ctx, 400, err.Error())
		return
	}

	utils.Success(ctx, gin.H{
		"image_url": imageURL,
		"record":    record,
	})
}

// GetSolveRecords 获取解题记录
func (c *SolveController) GetSolveRecords(ctx *gin.Context) {
	userID, _ := ctx.Get("user_id")

	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("page_size", "10"))

	records, total, err := c.solveService.GetSolveRecords(userID.(uint), page, pageSize)
	if err != nil {
		utils.InternalError(ctx, "获取解题记录失败")
		return
	}

	utils.Success(ctx, gin.H{
		"list":      records,
		"total":     total,
		"page":      page,
		"page_size": pageSize,
	})
}

// GetSolveRecord 获取单个解题记录
func (c *SolveController) GetSolveRecord(ctx *gin.Context) {
	recordID, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		utils.BadRequest(ctx, "无效的记录ID")
		return
	}

	record, err := c.solveService.GetSolveRecordByID(uint(recordID))
	if err != nil {
		utils.NotFound(ctx, "记录不存在")
		return
	}

	// 检查权限：只能查看自己的记录
	userID, _ := ctx.Get("user_id")
	if record.UserID != userID.(uint) {
		utils.Forbidden(ctx, "无权访问此记录")
		return
	}

	utils.Success(ctx, record)
}

// GetSolveStats 获取解题统计
func (c *SolveController) GetSolveStats(ctx *gin.Context) {
	userID, _ := ctx.Get("user_id")

	stats, err := c.solveService.GetSolveStats(userID.(uint))
	if err != nil {
		utils.InternalError(ctx, "获取统计信息失败")
		return
	}

	utils.Success(ctx, stats)
}

// GetAllSolveRecords 获取所有解题记录（管理员）
func (c *SolveController) GetAllSolveRecords(ctx *gin.Context) {
	userIDStr := ctx.Query("user_id")
	var userID uint = 0
	if userIDStr != "" {
		if id, err := strconv.ParseUint(userIDStr, 10, 32); err == nil {
			userID = uint(id)
		}
	}

	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("page_size", "10"))

	records, total, err := c.solveService.GetSolveRecords(userID, page, pageSize)
	if err != nil {
		utils.InternalError(ctx, "获取解题记录失败")
		return
	}

	utils.Success(ctx, gin.H{
		"list":      records,
		"total":     total,
		"page":      page,
		"page_size": pageSize,
	})
}

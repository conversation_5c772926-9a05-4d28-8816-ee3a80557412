package controllers

import (
	"solve-api/services"
	"solve-api/utils"
	"strconv"

	"github.com/gin-gonic/gin"
)

type PointCardController struct {
	pointCardService *services.PointCardService
}

func NewPointCardController(pointCardService *services.PointCardService) *PointCardController {
	return &PointCardController{
		pointCardService: pointCardService,
	}
}

// GeneratePointCardRequest 生成积分卡密请求结构
type GeneratePointCardRequest struct {
	Points     int64 `json:"points" binding:"required,min=1"`
	ExpireDays int   `json:"expire_days" binding:"min=0"`
}

// UsePointCardRequest 使用积分卡密请求结构
type UsePointCardRequest struct {
	Code string `json:"code" binding:"required"`
}

// GeneratePointCard 管理员生成积分卡密
func (c *PointCardController) GeneratePointCard(ctx *gin.Context) {
	adminID, _ := ctx.Get("user_id")

	var req GeneratePointCardRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(ctx, "参数错误: "+err.Error())
		return
	}

	pointCard, err := c.pointCardService.GeneratePointCard(adminID.(uint), req.Points, req.ExpireDays)
	if err != nil {
		utils.InternalError(ctx, "生成积分卡密失败")
		return
	}

	utils.SuccessWithMsg(ctx, "积分卡密生成成功", pointCard)
}

// UsePointCard 使用积分卡密
func (c *PointCardController) UsePointCard(ctx *gin.Context) {
	userID, _ := ctx.Get("user_id")

	var req UsePointCardRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(ctx, "参数错误: "+err.Error())
		return
	}

	if err := c.pointCardService.UsePointCard(userID.(uint), req.Code); err != nil {
		utils.Error(ctx, 400, err.Error())
		return
	}

	utils.SuccessWithMsg(ctx, "积分卡密使用成功", nil)
}

// GetPointCards 获取积分卡密列表
func (c *PointCardController) GetPointCards(ctx *gin.Context) {
	creatorID := uint(0)
	if creatorIDStr := ctx.Query("creator_id"); creatorIDStr != "" {
		if id, err := strconv.ParseUint(creatorIDStr, 10, 32); err == nil {
			creatorID = uint(id)
		}
	}

	creatorType := 0
	if creatorTypeStr := ctx.Query("creator_type"); creatorTypeStr != "" {
		if t, err := strconv.Atoi(creatorTypeStr); err == nil {
			creatorType = t
		}
	}

	status := -1
	if statusStr := ctx.Query("status"); statusStr != "" {
		if s, err := strconv.Atoi(statusStr); err == nil {
			status = s
		}
	}

	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("page_size", "10"))

	pointCards, total, err := c.pointCardService.GetPointCards(creatorID, creatorType, status, page, pageSize)
	if err != nil {
		utils.InternalError(ctx, "获取积分卡密列表失败")
		return
	}

	utils.Success(ctx, gin.H{
		"list":      pointCards,
		"total":     total,
		"page":      page,
		"page_size": pageSize,
	})
}

// GetMyPointCards 获取我的积分卡密列表（代理商）
func (c *PointCardController) GetMyPointCards(ctx *gin.Context) {
	creatorID, _ := ctx.Get("user_id")

	status := -1
	if statusStr := ctx.Query("status"); statusStr != "" {
		if s, err := strconv.Atoi(statusStr); err == nil {
			status = s
		}
	}

	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("page_size", "10"))

	pointCards, total, err := c.pointCardService.GetPointCards(creatorID.(uint), 2, status, page, pageSize)
	if err != nil {
		utils.InternalError(ctx, "获取积分卡密列表失败")
		return
	}

	utils.Success(ctx, gin.H{
		"list":      pointCards,
		"total":     total,
		"page":      page,
		"page_size": pageSize,
	})
}

// GetPointCardByCode 根据卡密获取信息
func (c *PointCardController) GetPointCardByCode(ctx *gin.Context) {
	code := ctx.Param("code")
	if code == "" {
		utils.BadRequest(ctx, "卡密不能为空")
		return
	}

	pointCard, err := c.pointCardService.GetPointCardByCode(code)
	if err != nil {
		utils.NotFound(ctx, "卡密不存在")
		return
	}

	utils.Success(ctx, pointCard)
}

// DisablePointCard 禁用积分卡密
func (c *PointCardController) DisablePointCard(ctx *gin.Context) {
	cardID, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		utils.BadRequest(ctx, "无效的卡密ID")
		return
	}

	if err := c.pointCardService.DisablePointCard(uint(cardID)); err != nil {
		utils.InternalError(ctx, "禁用积分卡密失败")
		return
	}

	utils.SuccessWithMsg(ctx, "积分卡密已禁用", nil)
}

// GetUsageStats 获取卡密使用统计
func (c *PointCardController) GetUsageStats(ctx *gin.Context) {
	creatorID := uint(0)
	if creatorIDStr := ctx.Query("creator_id"); creatorIDStr != "" {
		if id, err := strconv.ParseUint(creatorIDStr, 10, 32); err == nil {
			creatorID = uint(id)
		}
	}

	creatorType := 0
	if creatorTypeStr := ctx.Query("creator_type"); creatorTypeStr != "" {
		if t, err := strconv.Atoi(creatorTypeStr); err == nil {
			creatorType = t
		}
	}

	stats, err := c.pointCardService.GetUsageStats(creatorID, creatorType)
	if err != nil {
		utils.InternalError(ctx, "获取统计信息失败")
		return
	}

	utils.Success(ctx, stats)
}

// GetMyUsageStats 获取我的卡密使用统计（代理商）
func (c *PointCardController) GetMyUsageStats(ctx *gin.Context) {
	creatorID, _ := ctx.Get("user_id")

	stats, err := c.pointCardService.GetUsageStats(creatorID.(uint), 2)
	if err != nil {
		utils.InternalError(ctx, "获取统计信息失败")
		return
	}

	utils.Success(ctx, stats)
}

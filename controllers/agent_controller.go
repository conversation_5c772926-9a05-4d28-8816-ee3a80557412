package controllers

import (
	"solve-api/services"
	"solve-api/utils"
	"strconv"

	"github.com/gin-gonic/gin"
)

type AgentController struct {
	agentService *services.AgentService
}

func NewAgentController(agentService *services.AgentService) *AgentController {
	return &AgentController{
		agentService: agentService,
	}
}

// ApplyAgentRequest 申请代理商请求结构
type ApplyAgentRequest struct {
	Reason string `json:"reason" binding:"required,max=500"`
}

// ReviewApplicationRequest 审核申请请求结构
type ReviewApplicationRequest struct {
	Approved bool   `json:"approved"`
	Note     string `json:"note" binding:"max=500"`
}

// GeneratePointCardRequest 生成积分卡密请求结构
type GeneratePointCardRequest struct {
	Points     int64 `json:"points" binding:"required,min=1"`
	Price      int64 `json:"price" binding:"required,min=1"`
	ExpireDays int   `json:"expire_days" binding:"min=0"`
}

// ApplyAgent 申请成为代理商
func (c *AgentController) ApplyAgent(ctx *gin.Context) {
	userID, _ := ctx.Get("user_id")

	var req ApplyAgentRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(ctx, "参数错误: "+err.Error())
		return
	}

	if err := c.agentService.ApplyAgent(userID.(uint), req.Reason); err != nil {
		utils.Error(ctx, 400, err.Error())
		return
	}

	utils.SuccessWithMsg(ctx, "申请提交成功，请等待审核", nil)
}

// GetApplications 获取代理商申请列表（管理员）
func (c *AgentController) GetApplications(ctx *gin.Context) {
	status := -1
	if statusStr := ctx.Query("status"); statusStr != "" {
		if s, err := strconv.Atoi(statusStr); err == nil {
			status = s
		}
	}

	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("page_size", "10"))

	applications, total, err := c.agentService.GetApplications(status, page, pageSize)
	if err != nil {
		utils.InternalError(ctx, "获取申请列表失败")
		return
	}

	utils.Success(ctx, gin.H{
		"list":      applications,
		"total":     total,
		"page":      page,
		"page_size": pageSize,
	})
}

// ReviewApplication 审核代理商申请（管理员）
func (c *AgentController) ReviewApplication(ctx *gin.Context) {
	applicationID, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		utils.BadRequest(ctx, "无效的申请ID")
		return
	}

	reviewerID, _ := ctx.Get("user_id")

	var req ReviewApplicationRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(ctx, "参数错误: "+err.Error())
		return
	}

	if err := c.agentService.ReviewApplication(uint(applicationID), reviewerID.(uint), req.Approved, req.Note); err != nil {
		utils.Error(ctx, 400, err.Error())
		return
	}

	message := "申请已拒绝"
	if req.Approved {
		message = "申请已通过"
	}

	utils.SuccessWithMsg(ctx, message, nil)
}

// GeneratePointCard 代理商生成积分卡密
func (c *AgentController) GeneratePointCard(ctx *gin.Context) {
	agentID, _ := ctx.Get("user_id")

	var req GeneratePointCardRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(ctx, "参数错误: "+err.Error())
		return
	}

	pointCard, err := c.agentService.GeneratePointCard(agentID.(uint), req.Points, req.Price, req.ExpireDays)
	if err != nil {
		utils.Error(ctx, 400, err.Error())
		return
	}

	utils.SuccessWithMsg(ctx, "积分卡密生成成功", pointCard)
}

// GetAgentTransactions 获取代理商交易记录
func (c *AgentController) GetAgentTransactions(ctx *gin.Context) {
	agentID, _ := ctx.Get("user_id")

	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("page_size", "10"))

	transactions, total, err := c.agentService.GetAgentTransactions(agentID.(uint), page, pageSize)
	if err != nil {
		utils.InternalError(ctx, "获取交易记录失败")
		return
	}

	utils.Success(ctx, gin.H{
		"list":      transactions,
		"total":     total,
		"page":      page,
		"page_size": pageSize,
	})
}

// AddAgentBalance 增加代理商余额（管理员）
func (c *AgentController) AddAgentBalance(ctx *gin.Context) {
	agentID, err := strconv.ParseUint(ctx.Param("agent_id"), 10, 32)
	if err != nil {
		utils.BadRequest(ctx, "无效的代理商ID")
		return
	}

	var req struct {
		Amount      int64  `json:"amount" binding:"required,min=1"`
		Description string `json:"description" binding:"max=255"`
	}

	if err := ctx.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(ctx, "参数错误: "+err.Error())
		return
	}

	if err := c.agentService.AddAgentBalance(uint(agentID), req.Amount, req.Description); err != nil {
		utils.Error(ctx, 400, err.Error())
		return
	}

	utils.SuccessWithMsg(ctx, "余额充值成功", nil)
}

package controllers

import (
	"solve-api/config"
	"solve-api/services"
	"solve-api/utils"
	"strconv"

	"github.com/gin-gonic/gin"
)

type AuthController struct {
	userService *services.UserService
	config      *config.Config
}

func NewAuthController(userService *services.UserService, cfg *config.Config) *AuthController {
	return &AuthController{
		userService: userService,
		config:      cfg,
	}
}

// RegisterRequest 注册请求结构
type RegisterRequest struct {
	Username string `json:"username" binding:"required,min=3,max=20"`
	Password string `json:"password" binding:"required,min=6,max=20"`
	Nickname string `json:"nickname" binding:"max=50"`
}

// LoginRequest 登录请求结构
type LoginRequest struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
}

// WechatLoginRequest 微信登录请求结构
type WechatLoginRequest struct {
	OpenID   string `json:"open_id" binding:"required"`
	Nickname string `json:"nickname"`
	Avatar   string `json:"avatar"`
}

// AppLoginRequest App登录请求结构
type AppLoginRequest struct {
	AppUserID string `json:"app_user_id" binding:"required"`
	Nickname  string `json:"nickname"`
	Avatar    string `json:"avatar"`
}

// Register 用户注册
func (c *AuthController) Register(ctx *gin.Context) {
	var req RegisterRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(ctx, "参数错误: "+err.Error())
		return
	}

	user, err := c.userService.Register(req.Username, req.Password, req.Nickname)
	if err != nil {
		utils.Error(ctx, 400, err.Error())
		return
	}

	// 生成JWT token
	token, err := utils.GenerateJWT(user.ID, user.Username, user.IsAgent, 
		c.config.JWT.Secret, c.config.JWT.ExpireTime)
	if err != nil {
		utils.InternalError(ctx, "生成token失败")
		return
	}

	utils.Success(ctx, gin.H{
		"user":  user,
		"token": token,
	})
}

// Login 用户登录
func (c *AuthController) Login(ctx *gin.Context) {
	var req LoginRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(ctx, "参数错误: "+err.Error())
		return
	}

	user, err := c.userService.Login(req.Username, req.Password)
	if err != nil {
		utils.Error(ctx, 400, err.Error())
		return
	}

	// 生成JWT token
	token, err := utils.GenerateJWT(user.ID, user.Username, user.IsAgent, 
		c.config.JWT.Secret, c.config.JWT.ExpireTime)
	if err != nil {
		utils.InternalError(ctx, "生成token失败")
		return
	}

	utils.Success(ctx, gin.H{
		"user":  user,
		"token": token,
	})
}

// WechatLogin 微信登录
func (c *AuthController) WechatLogin(ctx *gin.Context) {
	var req WechatLoginRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(ctx, "参数错误: "+err.Error())
		return
	}

	user, err := c.userService.WechatLogin(req.OpenID, req.Nickname, req.Avatar)
	if err != nil {
		utils.Error(ctx, 400, err.Error())
		return
	}

	// 生成JWT token
	token, err := utils.GenerateJWT(user.ID, user.Username, user.IsAgent, 
		c.config.JWT.Secret, c.config.JWT.ExpireTime)
	if err != nil {
		utils.InternalError(ctx, "生成token失败")
		return
	}

	utils.Success(ctx, gin.H{
		"user":  user,
		"token": token,
	})
}

// AppLogin App登录
func (c *AuthController) AppLogin(ctx *gin.Context) {
	var req AppLoginRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(ctx, "参数错误: "+err.Error())
		return
	}

	user, err := c.userService.AppLogin(req.AppUserID, req.Nickname, req.Avatar)
	if err != nil {
		utils.Error(ctx, 400, err.Error())
		return
	}

	// 生成JWT token
	token, err := utils.GenerateJWT(user.ID, user.Username, user.IsAgent, 
		c.config.JWT.Secret, c.config.JWT.ExpireTime)
	if err != nil {
		utils.InternalError(ctx, "生成token失败")
		return
	}

	utils.Success(ctx, gin.H{
		"user":  user,
		"token": token,
	})
}

// GetProfile 获取用户信息
func (c *AuthController) GetProfile(ctx *gin.Context) {
	userID, _ := ctx.Get("user_id")
	
	user, err := c.userService.GetUserByID(userID.(uint))
	if err != nil {
		utils.NotFound(ctx, "用户不存在")
		return
	}

	utils.Success(ctx, user)
}

// UpdateProfile 更新用户信息
func (c *AuthController) UpdateProfile(ctx *gin.Context) {
	userID, _ := ctx.Get("user_id")

	var req struct {
		Nickname string `json:"nickname" binding:"max=50"`
		Avatar   string `json:"avatar" binding:"max=255"`
	}

	if err := ctx.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(ctx, "参数错误: "+err.Error())
		return
	}

	if err := c.userService.UpdateUser(userID.(uint), req.Nickname, req.Avatar); err != nil {
		utils.InternalError(ctx, "更新失败")
		return
	}

	utils.SuccessWithMsg(ctx, "更新成功", nil)
}

// RefreshToken 刷新token
func (c *AuthController) RefreshToken(ctx *gin.Context) {
	userID, _ := ctx.Get("user_id")
	username, _ := ctx.Get("username")
	isAgent, _ := ctx.Get("is_agent")

	// 生成新的JWT token
	token, err := utils.GenerateJWT(userID.(uint), username.(string), isAgent.(bool), 
		c.config.JWT.Secret, c.config.JWT.ExpireTime)
	if err != nil {
		utils.InternalError(ctx, "生成token失败")
		return
	}

	utils.Success(ctx, gin.H{
		"token": token,
	})
}

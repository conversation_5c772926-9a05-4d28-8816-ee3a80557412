package controllers

import (
	"solve-api/services"
	"solve-api/utils"
	"strconv"

	"github.com/gin-gonic/gin"
)

type UserController struct {
	userService *services.UserService
}

func NewUserController(userService *services.UserService) *UserController {
	return &UserController{
		userService: userService,
	}
}

// AddPointsRequest 增加积分请求结构
type AddPointsRequest struct {
	Points      int64  `json:"points" binding:"required,min=1"`
	Description string `json:"description" binding:"max=255"`
}

// GetUserInfo 获取用户信息
func (c *UserController) GetUserInfo(ctx *gin.Context) {
	userID, _ := ctx.Get("user_id")

	user, err := c.userService.GetUserByID(userID.(uint))
	if err != nil {
		utils.NotFound(ctx, "用户不存在")
		return
	}

	utils.Success(ctx, user)
}

// GetUserByID 根据ID获取用户信息（管理员）
func (c *UserController) GetUserByID(ctx *gin.Context) {
	userID, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		utils.BadRequest(ctx, "无效的用户ID")
		return
	}

	user, err := c.userService.GetUserByID(uint(userID))
	if err != nil {
		utils.NotFound(ctx, "用户不存在")
		return
	}

	utils.Success(ctx, user)
}

// AddUserPoints 增加用户积分（管理员）
func (c *UserController) AddUserPoints(ctx *gin.Context) {
	userID, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		utils.BadRequest(ctx, "无效的用户ID")
		return
	}

	var req AddPointsRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(ctx, "参数错误: "+err.Error())
		return
	}

	if err := c.userService.AddPoints(uint(userID), req.Points, req.Description); err != nil {
		utils.Error(ctx, 400, err.Error())
		return
	}

	utils.SuccessWithMsg(ctx, "积分充值成功", nil)
}

// BecomeAgent 成为代理商（管理员操作）
func (c *UserController) BecomeAgent(ctx *gin.Context) {
	userID, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		utils.BadRequest(ctx, "无效的用户ID")
		return
	}

	if err := c.userService.BecomeAgent(uint(userID)); err != nil {
		utils.Error(ctx, 400, err.Error())
		return
	}

	utils.SuccessWithMsg(ctx, "用户已成为代理商", nil)
}

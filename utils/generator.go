package utils

import (
	"crypto/rand"
	"fmt"
	"math/big"
	"strings"
	"time"
)

// GenerateCode 生成随机码
func GenerateCode(length int) string {
	const charset = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	b := make([]byte, length)
	for i := range b {
		n, _ := rand.Int(rand.Reader, big.NewInt(int64(len(charset))))
		b[i] = charset[n.Int64()]
	}
	return string(b)
}

// GenerateAgentCode 生成代理商编码
func GenerateAgentCode() string {
	timestamp := time.Now().Unix()
	randomPart := GenerateCode(6)
	return fmt.Sprintf("AG%d%s", timestamp%100000, randomPart)
}

// GeneratePointCardCode 生成积分卡密
func GeneratePointCardCode() string {
	// 生成格式：XXXX-XXXX-XXXX-XXXX
	parts := make([]string, 4)
	for i := 0; i < 4; i++ {
		parts[i] = GenerateCode(4)
	}
	return strings.Join(parts, "-")
}

// GenerateUsername 生成用户名
func GenerateUsername(prefix string) string {
	timestamp := time.Now().Unix()
	randomPart := GenerateCode(4)
	return fmt.Sprintf("%s%d%s", prefix, timestamp%10000, randomPart)
}

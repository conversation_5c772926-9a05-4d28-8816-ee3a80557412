#!/bin/bash

# 简化版启动脚本（不依赖外部网络）

echo "=== 拍照搜题API服务启动脚本（简化版）==="

# 设置环境变量
export GIN_MODE=debug
export MYSQL_HOST=***********
export MYSQL_PORT=3380
export MYSQL_USERNAME=gmdns
export MYSQL_PASSWORD=Suyan15913..
export MYSQL_DATABASE=solve_web
export JWT_SECRET=solve-api-jwt-secret-2024

echo "环境变量设置完成"

# 检查Go环境
if ! command -v go &> /dev/null; then
    echo "错误: 未找到Go环境，请先安装Go"
    exit 1
fi

# 创建上传目录
mkdir -p uploads/solve

# 直接运行（不下载依赖）
echo "启动服务..."
echo "服务将运行在 http://localhost:8080"
echo "数据库: solve_web@***********:3380"
echo "文件上传: 本地模拟OSS"
echo "按 Ctrl+C 停止服务"
echo "=========================="

go run main.go

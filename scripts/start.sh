#!/bin/bash

# 拍照搜题API服务启动脚本

echo "=== 拍照搜题API服务启动脚本 ==="

# 检查Go环境
if ! command -v go &> /dev/null; then
    echo "错误: 未找到Go环境，请先安装Go"
    exit 1
fi

# 检查MySQL连接
echo "检查MySQL连接..."
if ! command -v mysql &> /dev/null; then
    echo "警告: 未找到mysql命令，请确保MySQL已安装并运行"
else
    # 这里可以添加MySQL连接测试
    echo "MySQL检查完成"
fi

# 检查Redis连接
echo "检查Redis连接..."
if ! command -v redis-cli &> /dev/null; then
    echo "警告: 未找到redis-cli命令，请确保Redis已安装并运行"
else
    # 这里可以添加Redis连接测试
    echo "Redis检查完成"
fi

# 安装依赖
echo "安装Go依赖..."
go mod tidy

if [ $? -ne 0 ]; then
    echo "错误: 依赖安装失败"
    exit 1
fi

# 检查配置文件
if [ ! -f "config.yaml" ]; then
    echo "警告: 未找到config.yaml配置文件，将使用默认配置"
fi

# 编译项目
echo "编译项目..."
go build -o solve-api main.go

if [ $? -ne 0 ]; then
    echo "错误: 项目编译失败"
    exit 1
fi

# 启动服务
echo "启动服务..."
echo "服务将运行在 http://localhost:8080"
echo "按 Ctrl+C 停止服务"
echo "=========================="

./solve-api

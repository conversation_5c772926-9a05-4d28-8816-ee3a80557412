-- 创建数据库
CREATE DATABASE IF NOT EXISTS solve_api CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE solve_api;

-- 用户表
CREATE TABLE IF NOT EXISTS `users` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `created_at` datetime(3) DEFAULT NULL,
  `updated_at` datetime(3) DEFAULT NULL,
  `deleted_at` datetime(3) DEFAULT NULL,
  `username` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `nickname` varchar(50) DEFAULT NULL,
  `avatar` varchar(255) DEFAULT NULL,
  `wechat_open_id` varchar(100) DEFAULT NULL,
  `app_user_id` varchar(100) DEFAULT NULL,
  `balance` bigint DEFAULT '0' COMMENT '余额(分)',
  `points` bigint DEFAULT '0' COMMENT '积分',
  `is_agent` tinyint(1) DEFAULT '0',
  `agent_at` datetime(3) DEFAULT NULL,
  `agent_code` varchar(20) DEFAULT NULL,
  `status` int DEFAULT '1' COMMENT '1正常 0禁用',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_users_username` (`username`),
  UNIQUE KEY `idx_users_wechat_open_id` (`wechat_open_id`),
  UNIQUE KEY `idx_users_app_user_id` (`app_user_id`),
  UNIQUE KEY `idx_users_agent_code` (`agent_code`),
  KEY `idx_users_deleted_at` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 代理商申请表
CREATE TABLE IF NOT EXISTS `agent_applications` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `created_at` datetime(3) DEFAULT NULL,
  `updated_at` datetime(3) DEFAULT NULL,
  `deleted_at` datetime(3) DEFAULT NULL,
  `user_id` bigint unsigned NOT NULL,
  `reason` text COMMENT '申请理由',
  `status` int DEFAULT '0' COMMENT '0待审核 1通过 2拒绝',
  `reviewer_id` bigint unsigned DEFAULT NULL COMMENT '审核人ID',
  `review_at` datetime(3) DEFAULT NULL,
  `review_note` text COMMENT '审核备注',
  PRIMARY KEY (`id`),
  KEY `idx_agent_applications_user_id` (`user_id`),
  KEY `idx_agent_applications_deleted_at` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 积分卡密表
CREATE TABLE IF NOT EXISTS `point_cards` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `created_at` datetime(3) DEFAULT NULL,
  `updated_at` datetime(3) DEFAULT NULL,
  `deleted_at` datetime(3) DEFAULT NULL,
  `code` varchar(32) NOT NULL COMMENT '卡密',
  `points` bigint NOT NULL COMMENT '积分数量',
  `price` bigint NOT NULL COMMENT '价格(分)',
  `creator_id` bigint unsigned NOT NULL COMMENT '生成者ID',
  `creator_type` int NOT NULL COMMENT '1管理员 2代理商',
  `is_used` tinyint(1) DEFAULT '0',
  `user_id` bigint unsigned DEFAULT NULL COMMENT '使用者ID',
  `used_at` datetime(3) DEFAULT NULL,
  `expire_at` datetime(3) DEFAULT NULL COMMENT '过期时间',
  `status` int DEFAULT '1' COMMENT '1有效 0无效',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_point_cards_code` (`code`),
  KEY `idx_point_cards_creator_id` (`creator_id`),
  KEY `idx_point_cards_user_id` (`user_id`),
  KEY `idx_point_cards_deleted_at` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 解题记录表
CREATE TABLE IF NOT EXISTS `solve_records` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `created_at` datetime(3) DEFAULT NULL,
  `updated_at` datetime(3) DEFAULT NULL,
  `deleted_at` datetime(3) DEFAULT NULL,
  `user_id` bigint unsigned NOT NULL,
  `image_url` varchar(500) NOT NULL COMMENT '图片URL',
  `request_data` text COMMENT '请求数据',
  `response_data` text COMMENT '响应数据',
  `is_success` tinyint(1) DEFAULT '0' COMMENT '是否成功',
  `error_msg` varchar(500) DEFAULT NULL COMMENT '错误信息',
  `points_cost` bigint DEFAULT '0' COMMENT '消耗积分',
  `is_deducted` tinyint(1) DEFAULT '0' COMMENT '是否已扣费',
  `third_api_request_id` varchar(100) DEFAULT NULL COMMENT '第三方API请求ID',
  `third_api_response_at` datetime(3) DEFAULT NULL,
  `third_api_latency` bigint DEFAULT NULL COMMENT '第三方API延迟(毫秒)',
  `status` int DEFAULT '1' COMMENT '1正常 0异常',
  PRIMARY KEY (`id`),
  KEY `idx_solve_records_user_id` (`user_id`),
  KEY `idx_solve_records_deleted_at` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 用户交易记录表
CREATE TABLE IF NOT EXISTS `user_transactions` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `created_at` datetime(3) DEFAULT NULL,
  `updated_at` datetime(3) DEFAULT NULL,
  `deleted_at` datetime(3) DEFAULT NULL,
  `user_id` bigint unsigned NOT NULL,
  `type` int NOT NULL COMMENT '1积分消费 2积分充值 3余额充值 4卡密兑换',
  `amount` bigint NOT NULL COMMENT '金额(分)或积分数',
  `points` bigint DEFAULT '0' COMMENT '积分变动',
  `balance` bigint DEFAULT '0' COMMENT '余额变动(分)',
  `description` varchar(255) DEFAULT NULL COMMENT '描述',
  `related_id` bigint unsigned DEFAULT NULL COMMENT '关联ID',
  `related_type` varchar(50) DEFAULT NULL COMMENT '关联类型',
  `balance_before` bigint DEFAULT NULL COMMENT '交易前余额',
  `balance_after` bigint DEFAULT NULL COMMENT '交易后余额',
  `points_before` bigint DEFAULT NULL COMMENT '交易前积分',
  `points_after` bigint DEFAULT NULL COMMENT '交易后积分',
  `status` int DEFAULT '1' COMMENT '1成功 0失败',
  PRIMARY KEY (`id`),
  KEY `idx_user_transactions_user_id` (`user_id`),
  KEY `idx_user_transactions_deleted_at` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 代理商交易记录表
CREATE TABLE IF NOT EXISTS `agent_transactions` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `created_at` datetime(3) DEFAULT NULL,
  `updated_at` datetime(3) DEFAULT NULL,
  `deleted_at` datetime(3) DEFAULT NULL,
  `agent_id` bigint unsigned NOT NULL,
  `type` int NOT NULL COMMENT '1生成卡密 2余额充值',
  `amount` bigint NOT NULL COMMENT '金额(分)',
  `points` bigint DEFAULT '0' COMMENT '积分数量',
  `description` varchar(255) DEFAULT NULL COMMENT '描述',
  `related_id` bigint unsigned DEFAULT NULL COMMENT '关联ID',
  `related_type` varchar(50) DEFAULT NULL COMMENT '关联类型',
  `balance_before` bigint DEFAULT NULL COMMENT '交易前余额',
  `balance_after` bigint DEFAULT NULL COMMENT '交易后余额',
  `status` int DEFAULT '1' COMMENT '1成功 0失败',
  PRIMARY KEY (`id`),
  KEY `idx_agent_transactions_agent_id` (`agent_id`),
  KEY `idx_agent_transactions_deleted_at` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 插入默认管理员用户（用户名: admin, 密码: admin123）
INSERT INTO `users` (`username`, `password`, `nickname`, `status`, `created_at`, `updated_at`) 
VALUES ('admin', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '管理员', 1, NOW(), NOW())
ON DUPLICATE KEY UPDATE `username` = `username`;

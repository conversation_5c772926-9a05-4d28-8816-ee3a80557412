# 拍照搜题后端API服务

基于Go + Gin框架开发的拍照搜题后端API服务，支持用户注册登录、代理商系统、积分卡密管理、图片上传解题等功能。

## 功能特性

### 1. 用户系统
- 用户注册与登录
- 微信小程序登录
- App端登录
- 双端账号打通
- 用户信息管理（头像、昵称、余额、积分）

### 2. 代理商系统
- 用户申请成为代理商
- 管理员审核代理商申请
- 代理商使用余额生成积分卡密
- 代理商交易记录管理

### 3. 积分卡密系统
- 管理员生成积分卡密
- 代理商生成积分卡密
- 用户使用卡密兑换积分
- 卡密使用统计

### 4. 解题模块
- 图片上传到阿里云OSS
- 调用第三方解题API
- 成功解题扣除积分
- 失败解题不扣除积分

### 5. 记录系统
- 解题记录管理
- 用户积分余额变动记录
- 代理商交易记录

## 技术栈

- **后端框架**: Gin
- **数据库**: MySQL 8
- **缓存**: Redis
- **文件存储**: 阿里云OSS
- **认证**: JWT
- **ORM**: GORM

## 项目结构

```
solve-api/
├── config/           # 配置管理
├── controllers/      # 控制器层
├── database/         # 数据库连接
├── middleware/       # 中间件
├── models/          # 数据模型
├── router/          # 路由配置
├── services/        # 业务逻辑层
├── utils/           # 工具函数
├── config.yaml      # 配置文件
├── main.go          # 程序入口
└── README.md        # 项目说明
```

## 快速开始

### 1. 环境要求

- Go 1.21+
- MySQL 8.0+
- Redis 6.0+

### 2. 安装依赖

```bash
go mod tidy
```

### 3. 配置文件

复制并修改配置文件：

```bash
cp config.yaml.example config.yaml
```

修改 `config.yaml` 中的数据库、Redis、OSS等配置信息。

### 4. 数据库初始化

创建数据库：

```sql
CREATE DATABASE solve_api CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

程序启动时会自动创建表结构。

### 5. 启动服务

```bash
go run main.go
```

服务默认运行在 `http://localhost:8080`

## API文档

### 认证相关

#### 用户注册
```
POST /api/v1/auth/register
Content-Type: application/json

{
  "username": "testuser",
  "password": "123456",
  "nickname": "测试用户"
}
```

#### 用户登录
```
POST /api/v1/auth/login
Content-Type: application/json

{
  "username": "testuser",
  "password": "123456"
}
```

#### 微信登录
```
POST /api/v1/auth/wechat-login
Content-Type: application/json

{
  "open_id": "wx_openid_123",
  "nickname": "微信用户",
  "avatar": "https://example.com/avatar.jpg"
}
```

### 解题相关

#### 上传图片
```
POST /api/v1/solve/upload
Authorization: Bearer <token>
Content-Type: multipart/form-data

image: <file>
```

#### 解题
```
POST /api/v1/solve/problem
Authorization: Bearer <token>
Content-Type: application/json

{
  "image_url": "https://example.com/image.jpg"
}
```

#### 上传并解题
```
POST /api/v1/solve/upload-and-solve
Authorization: Bearer <token>
Content-Type: multipart/form-data

image: <file>
```

### 积分卡密相关

#### 使用积分卡密
```
POST /api/v1/point-cards/use
Authorization: Bearer <token>
Content-Type: application/json

{
  "code": "XXXX-XXXX-XXXX-XXXX"
}
```

#### 查询卡密信息
```
GET /api/v1/point-cards/{code}
```

### 代理商相关

#### 申请成为代理商
```
POST /api/v1/agent/apply
Authorization: Bearer <token>
Content-Type: application/json

{
  "reason": "申请理由"
}
```

#### 生成积分卡密（代理商）
```
POST /api/v1/agent/point-cards
Authorization: Bearer <token>
Content-Type: application/json

{
  "points": 100,
  "price": 1000,
  "expire_days": 30
}
```

## 环境变量

可以通过环境变量覆盖配置文件中的设置：

- `SERVER_PORT`: 服务端口
- `GIN_MODE`: Gin运行模式 (debug/release)
- `MYSQL_HOST`: MySQL主机地址
- `MYSQL_PASSWORD`: MySQL密码
- `REDIS_HOST`: Redis主机地址
- `JWT_SECRET`: JWT密钥

## 部署

### Docker部署

```bash
# 构建镜像
docker build -t solve-api .

# 运行容器
docker run -d \
  --name solve-api \
  -p 8080:8080 \
  -e MYSQL_HOST=mysql \
  -e REDIS_HOST=redis \
  solve-api
```

### 生产环境配置

1. 设置 `GIN_MODE=release`
2. 配置反向代理（Nginx）
3. 设置HTTPS证书
4. 配置日志收集
5. 设置监控告警

## 开发说明

### 添加新功能

1. 在 `models/` 中定义数据模型
2. 在 `services/` 中实现业务逻辑
3. 在 `controllers/` 中实现API接口
4. 在 `router/` 中配置路由
5. 编写测试用例

### 代码规范

- 使用 `gofmt` 格式化代码
- 遵循Go命名规范
- 添加必要的注释
- 编写单元测试

## 许可证

MIT License
